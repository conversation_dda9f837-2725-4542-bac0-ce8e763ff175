from flask import Flask, render_template, request, flash
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this in production

def calculate_value(fipe_value, vehicle_type):
    """
    Calculate the final value based on FIPE and vehicle type.
    
    Logic:
    1. Calculate 60% of FIPE value
    2. Add 5% increment to that value
    3. Add vehicle-specific fee:
       - moto: R$ 500,00
       - carro/utilitario: R$ 1.900,00
       - pesado: R$ 4.500,00
    """
    try:
        # Convert FIPE value to float
        fipe = float(fipe_value.replace(',', '.').replace('R$', '').replace(' ', ''))
        
        # Calculate 60% of FIPE value
        base_value = fipe * 0.60
        
        # Add 5% increment
        incremented_value = base_value * 1.05
        
        # Add vehicle-specific fee
        vehicle_fees = {
            'moto': 500.00,
            'carro/utilitario': 1900.00,
            'pesado': 4500.00
        }
        
        final_value = incremented_value + vehicle_fees.get(vehicle_type, 1900.00)
        
        return final_value
        
    except (ValueError, AttributeError):
        return None

@app.route('/', methods=['GET', 'POST'])
def index():
    result = None
    
    if request.method == 'POST':
        fipe_value = request.form.get('fipe_value', '').strip()
        vehicle_type = request.form.get('vehicle_type', 'carro/utilitario')
        
        # Validate inputs
        if not fipe_value:
            flash('Por favor, insira o valor FIPE.', 'error')
        else:
            calculated_value = calculate_value(fipe_value, vehicle_type)
            
            if calculated_value is None:
                flash('Valor FIPE inválido. Por favor, insira um número válido.', 'error')
            else:
                result = {
                    'fipe_input': fipe_value,
                    'vehicle_type': vehicle_type,
                    'calculated_value': calculated_value
                }
    
    return render_template('index.html', result=result)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
