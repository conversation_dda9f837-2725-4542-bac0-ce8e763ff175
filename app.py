from flask import Flask, render_template, request, flash
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this in production

def calculate_value(fipe_value, vehicle_type):
    """
    Calculate the final value based on FIPE and vehicle type.

    Logic:
    The final result should be 60% of FIPE, but this 60% must consider:
    1. 5% leiloeiro fee
    2. Vehicle-specific fee:
       - moto: R$ 500,00
       - carro/utilitario: R$ 1.900,00
       - pesado: R$ 4.500,00

    Formula: (base_amount + vehicle_fee) * 1.05 = 60% of FIPE
    So: base_amount = (60% of FIPE / 1.05) - vehicle_fee
    Final: (base_amount + vehicle_fee) * 1.05
    """
    try:
        # Convert FIPE value to float
        fipe = float(fipe_value.replace(',', '.').replace('R$', '').replace(' ', ''))

        # Target is 60% of FIPE value
        target_value = fipe * 0.60

        # Vehicle-specific fees
        vehicle_fees = {
            'moto': 500.00,
            'carro/utilitario': 1900.00,
            'pesado': 4500.00
        }
        
        vehicle_fee = vehicle_fees.get(vehicle_type)

        # Work backwards: target = (base + fee) * 1.05
        # So: base = (target / 1.05) - fee
        base_amount = (target_value / 1.05) - vehicle_fee

        # Final calculation: (base + fee) * 1.05 = target (60% of FIPE)
        final_value = (base_amount + vehicle_fee) * 1.05

        return base_amount, final_value

    except (ValueError, AttributeError):
        return None

@app.route('/', methods=['GET', 'POST'])
def index():
    result = None
    
    if request.method == 'POST':
        fipe_value = request.form.get('fipe_value', '').strip()
        vehicle_type = request.form.get('vehicle_type', 'carro/utilitario')
        
        # Validate inputs
        if not fipe_value:
            flash('Por favor, insira o valor FIPE.', 'error')
        else:
            calculated_value = calculate_value(fipe_value, vehicle_type)
            
            if calculated_value is None:
                flash('Valor FIPE inválido. Por favor, insira um número válido.', 'error')
            else:
                result = {
                    'fipe_input': fipe_value,
                    'vehicle_type': vehicle_type,
                    'lance_recomendado': calculated_value[0],
                    'valor_final': calculated_value[1]
                }
    
    return render_template('index.html', result=result)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
