<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora FIPE</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, select:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        .submit-btn:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #e8f5e8;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #d32f2f;
        }
        .result-item {
            margin-bottom: 10px;
        }
        .result-value {
            font-size: 24px;
            font-weight: bold;
            color: #2e7d32;
        }
        .required {
            color: red;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Calculadora FIPE</h1>
        
        <!-- Display flash messages -->
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="error">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="form-group">
                <label for="fipe_value">Valor FIPE <span class="required">*</span></label>
                <input type="text" 
                       id="fipe_value" 
                       name="fipe_value" 
                       placeholder="Ex: 50000,00 ou R$ 50.000,00"
                       value="{{ request.form.fipe_value if request.form.fipe_value }}"
                       required>
            </div>
            
            <div class="form-group">
                <label for="vehicle_type">Tipo de Veículo <span class="required">*</span></label>
                <select id="vehicle_type" name="vehicle_type" required>
                    <option value="moto" {{ 'selected' if request.form.vehicle_type == 'moto' }}>Moto</option>
                    <option value="carro/utilitario" {{ 'selected' if request.form.vehicle_type == 'carro/utilitario' or not request.form.vehicle_type }}>Carro/Utilitário</option>
                    <option value="pesado" {{ 'selected' if request.form.vehicle_type == 'pesado' }}>Pesado</option>
                </select>
            </div>
            
            <button type="submit" class="submit-btn">Calcular</button>
        </form>
        
        {% if result %}
        <div class="result">
            <h3>Resultado do Cálculo</h3>
            <div class="result-item">
                <strong>Valor FIPE informado:</strong> {{ result.fipe_input }}
            </div>
            <div class="result-item">
                <strong>Tipo de veículo:</strong> {{ result.vehicle_type|title }}
            </div>
            <div class="result-item">
                <strong>Valor calculado:</strong> 
                <span class="result-value">R$ {{ "%.2f"|format(result.calculated_value)|replace('.', ',') }}</span>
            </div>
            
            <div style="margin-top: 15px; font-size: 14px; color: #666;">
                <strong>Cálculo aplicado:</strong><br>
                • 60% do valor FIPE<br>
                • + 5% de incremento<br>
                • + Taxa do veículo (Moto: R$ 500, Carro/Utilitário: R$ 1.900, Pesado: R$ 4.500)
            </div>
        </div>
        {% endif %}
    </div>
</body>
</html>
